'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Plus, Search, Filter, MoreHorizontal, Eye, Edit, Trash2, Copy, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { format } from 'date-fns'
import { AdminDataTable, BulkActions, StatsCards } from '@/components/admin'

interface Page {
  id: string
  title: string
  slug: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  author: {
    id: string
    name: string
    image?: string
  }
  category?: {
    id: string
    name: string
    color?: string
  }
  _count: {
    views: number
    comments: number
  }
}

const MOCK_PAGES: Page[] = [
  {
    id: '1',
    title: 'Welcome to Our Platform',
    slug: 'welcome',
    template: 'landing',
    status: 'PUBLISHED',
    publishedAt: new Date('2024-01-15'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15'),
    author: {
      id: '1',
      name: 'John Doe',
      image: '/avatars/john.jpg'
    },
    category: {
      id: '1',
      name: 'Marketing',
      color: '#3b82f6'
    },
    _count: {
      views: 1250,
      comments: 8
    }
  },
  {
    id: '2',
    title: 'About Our Company',
    slug: 'about',
    template: 'about',
    status: 'PUBLISHED',
    publishedAt: new Date('2024-01-12'),
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-12'),
    author: {
      id: '2',
      name: 'Jane Smith',
      image: '/avatars/jane.jpg'
    },
    category: {
      id: '2',
      name: 'Company',
      color: '#10b981'
    },
    _count: {
      views: 890,
      comments: 3
    }
  },
  {
    id: '3',
    title: 'Contact Information',
    slug: 'contact',
    template: 'contact',
    status: 'DRAFT',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    author: {
      id: '1',
      name: 'John Doe',
      image: '/avatars/john.jpg'
    },
    _count: {
      views: 0,
      comments: 0
    }
  }
]

export default function PagesAdminPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [pages, setPages] = useState<Page[]>([])
  const [filteredPages, setFilteredPages] = useState<Page[]>([])
  const [selectedPages, setSelectedPages] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [templateFilter, setTemplateFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  // Fetch pages from API
  useEffect(() => {
    fetchPages()
  }, [pagination.page, pagination.limit, statusFilter, templateFilter, searchQuery])

  const fetchPages = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      })

      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }
      if (templateFilter !== 'all') {
        params.append('template', templateFilter)
      }
      if (searchQuery) {
        params.append('search', searchQuery)
      }

      const response = await fetch(`/api/pages?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch pages')
      }

      const data = await response.json()
      setPages(data.pages)
      setPagination(prev => ({
        ...prev,
        total: data.pagination.total,
        totalPages: data.pagination.totalPages
      }))
    } catch (error) {
      console.error('Failed to fetch pages:', error)
      toast({
        title: 'Error',
        description: 'Failed to load pages.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Update filtered pages when pages change (API handles filtering)
  useEffect(() => {
    setFilteredPages(pages)
  }, [pages])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (pagination.page !== 1) {
        setPagination(prev => ({ ...prev, page: 1 }))
      } else {
        fetchPages()
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [searchQuery])

  const handleCreatePage = () => {
    router.push('/admin/pages/new')
  }

  const handleEditPage = (pageId: string) => {
    router.push(`/admin/pages/${pageId}/edit`)
  }

  const handleViewPage = (slug: string) => {
    window.open(`/${slug}`, '_blank')
  }

  const handleDuplicatePage = async (pageId: string) => {
    try {
      setLoading(true)

      // Get the page to duplicate
      const pageToClone = pages.find(p => p.id === pageId)
      if (!pageToClone) {
        throw new Error('Page not found')
      }

      // Create a new page with duplicated data
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `${pageToClone.title} (Copy)`,
          slug: `${pageToClone.slug}-copy-${Date.now()}`,
          content: pageToClone.content,
          template: pageToClone.template,
          status: 'DRAFT',
          seoTitle: pageToClone.seoTitle,
          seoDescription: pageToClone.seoDescription,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate page')
      }

      await fetchPages() // Refresh the list
      toast({
        title: 'Page duplicated',
        description: 'The page has been successfully duplicated.',
      })
    } catch (error) {
      console.error('Failed to duplicate page:', error)
      toast({
        title: 'Error',
        description: 'Failed to duplicate page.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePage = async (pageId: string) => {
    if (!confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      return
    }

    try {
      setLoading(true)

      const pageToDelete = pages.find(p => p.id === pageId)
      if (!pageToDelete) {
        throw new Error('Page not found')
      }

      const response = await fetch(`/api/pages/${pageToDelete.slug}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete page')
      }

      await fetchPages() // Refresh the list
      toast({
        title: 'Page deleted',
        description: 'The page has been successfully deleted.',
      })
    } catch (error) {
      console.error('Failed to delete page:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete page.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBulkAction = async (action: string, pageIds: string[]) => {
    if (action === 'delete' && !confirm(`Are you sure you want to delete ${pageIds.length} page(s)? This action cannot be undone.`)) {
      return
    }

    try {
      setLoading(true)

      // Process each page individually
      const promises = pageIds.map(async (pageId) => {
        const page = pages.find(p => p.id === pageId)
        if (!page) return

        switch (action) {
          case 'publish':
          case 'draft':
          case 'archive':
            const status = action === 'publish' ? 'PUBLISHED' : action === 'draft' ? 'DRAFT' : 'ARCHIVED'
            const response = await fetch(`/api/pages/${page.slug}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ status }),
            })
            if (!response.ok) {
              throw new Error(`Failed to ${action} page: ${page.title}`)
            }
            break
          case 'delete':
            const deleteResponse = await fetch(`/api/pages/${page.slug}`, {
              method: 'DELETE',
            })
            if (!deleteResponse.ok) {
              throw new Error(`Failed to delete page: ${page.title}`)
            }
            break
        }
      })

      await Promise.all(promises)
      await fetchPages() // Refresh the list
      setSelectedPages([])

      toast({
        title: 'Bulk action completed',
        description: `Successfully ${action}ed ${pageIds.length} page(s).`,
      })
    } catch (error) {
      console.error('Failed to perform bulk action:', error)
      toast({
        title: 'Error',
        description: 'Failed to perform bulk action.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: Page['status']) => {
    const variants = {
      PUBLISHED: 'default',
      DRAFT: 'secondary',
      ARCHIVED: 'outline'
    } as const

    const colors = {
      PUBLISHED: 'text-green-700 bg-green-50 border-green-200',
      DRAFT: 'text-yellow-700 bg-yellow-50 border-yellow-200',
      ARCHIVED: 'text-gray-700 bg-gray-50 border-gray-200'
    }

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.toLowerCase()}
      </Badge>
    )
  }

  const getTemplateBadge = (template: string) => {
    const colors = {
      default: 'bg-blue-50 text-blue-700',
      landing: 'bg-purple-50 text-purple-700',
      about: 'bg-green-50 text-green-700',
      contact: 'bg-orange-50 text-orange-700',
      blocks: 'bg-pink-50 text-pink-700'
    }

    return (
      <Badge variant="outline" className={colors[template as keyof typeof colors] || colors.default}>
        {template}
      </Badge>
    )
  }

  const columns = [
    {
      id: 'select',
      header: ({ table }: any) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedPages(filteredPages.map(page => page.id))
            } else {
              setSelectedPages([])
            }
          }}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }: any) => (
        <input
          type="checkbox"
          checked={selectedPages.includes(row.original.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedPages([...selectedPages, row.original.id])
            } else {
              setSelectedPages(selectedPages.filter(id => id !== row.original.id))
            }
          }}
          className="rounded border-gray-300"
        />
      ),
    },
    {
      accessorKey: 'title',
      header: 'Title',
      cell: ({ row }: any) => {
        const page = row.original
        return (
          <div className="flex items-center space-x-3">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <div className="space-y-1">
              <div className="font-medium">{page.title}</div>
              <div className="text-sm text-muted-foreground">/{page.slug}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'author',
      header: 'Author',
      cell: ({ row }: any) => {
        const author = row.original.author
        return (
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={author.image} alt={author.name} />
              <AvatarFallback className="text-xs">
                {author.name.split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{author.name}</span>
          </div>
        )
      },
    },
    {
      accessorKey: 'template',
      header: 'Template',
      cell: ({ row }: any) => getTemplateBadge(row.original.template),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: any) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }: any) => {
        const category = row.original.category
        return category ? (
          <Badge
            variant="outline"
            style={{
              borderColor: category.color,
              color: category.color
            }}
          >
            {category.name}
          </Badge>
        ) : (
          <span className="text-muted-foreground">—</span>
        )
      },
    },
    {
      accessorKey: 'stats',
      header: 'Stats',
      cell: ({ row }: any) => {
        const stats = row.original._count
        return (
          <div className="text-sm text-muted-foreground">
            <div>{stats.views} views</div>
            <div>{stats.comments} comments</div>
          </div>
        )
      },
    },
    {
      accessorKey: 'updatedAt',
      header: 'Last Modified',
      cell: ({ row }: any) => (
        <div className="text-sm text-muted-foreground">
          {format(row.original.updatedAt, 'MMM dd, yyyy')}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: any) => {
        const page = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleViewPage(page.slug)}>
                <Eye className="mr-2 h-4 w-4" />
                View Page
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditPage(page.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicatePage(page.id)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeletePage(page.id)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const stats = [
    {
      title: 'Total Pages',
      value: pagination.total.toString(),
      description: 'All pages in the system',
      trend: { value: 12, isPositive: true }
    },
    {
      title: 'Published',
      value: pages.filter(p => p.status === 'PUBLISHED').length.toString(),
      description: 'Live pages',
      trend: { value: 8, isPositive: true }
    },
    {
      title: 'Drafts',
      value: pages.filter(p => p.status === 'DRAFT').length.toString(),
      description: 'Unpublished pages',
      trend: { value: 3, isPositive: false }
    },
    {
      title: 'Total Views',
      value: pages.reduce((sum, p) => sum + (p._count?.views || 0), 0).toLocaleString(),
      description: 'All-time page views',
      trend: { value: 15, isPositive: true }
    }
  ]

  const bulkActions = [
    { value: 'publish', label: 'Publish' },
    { value: 'draft', label: 'Move to Draft' },
    { value: 'archive', label: 'Archive' },
    { value: 'delete', label: 'Delete', destructive: true }
  ]

  return (
    <div className="space-y-6">
      {/* Stats */}
      <StatsCards stats={stats} className="py-2 px-4"/>

      {/* Bulk Actions */}
      {selectedPages.length > 0 && (
        <BulkActions
          selectedCount={selectedPages.length}
          actions={bulkActions}
          onAction={(action) => handleBulkAction(action, selectedPages)}
          onClear={() => setSelectedPages([])}
        />
      )}

      {/* Filters and Search */}
      <Card className="border-none">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Pages</CardTitle>
            <CardDescription>
              A list of all pages in your website
            </CardDescription>
          </div>
          <Button onClick={handleCreatePage} className="ml-auto">
            <Plus className="mr-2 h-4 w-4" />
            New Page
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search pages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PUBLISHED">Published</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="ARCHIVED">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={templateFilter} onValueChange={setTemplateFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Templates</SelectItem>
                <SelectItem value="default">Default</SelectItem>
                <SelectItem value="landing">Landing</SelectItem>
                <SelectItem value="about">About</SelectItem>
                <SelectItem value="contact">Contact</SelectItem>
                <SelectItem value="blocks">Blocks</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <AdminDataTable
            columns={columns}
            data={filteredPages}
            isLoading={loading}
            pagination={{
              page: pagination.page,
              totalPages: pagination.totalPages,
              onPageChange: (page) => setPagination(prev => ({ ...prev, page }))
            }}
          />
        </CardContent>
      </Card>

      {/* Floating Action Button for Mobile */}
      <div className="fixed bottom-6 right-6 md:hidden">
        <Button
          onClick={handleCreatePage}
          size="lg"
          className="rounded-full h-14 w-14 shadow-lg"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>
    </div>
  )
}
